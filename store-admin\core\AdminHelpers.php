<?php

/**
 * AdminHelpers Class
 * Utility methods for the store-admin interface
 */

class AdminHelpers
{
    /**
     * Get search and pagination parameters from request
     */
    public static function getSearchParams(): array
    {
        return [
            'search' => $_GET['search'] ?? '',
            'page_num' => max(1, (int)($_GET['page_num'] ?? 1)),
            'per_page' => max(5, min(100, (int)($_GET['per_page'] ?? 10)))
        ];
    }

    /**
     * Build search WHERE clause and parameters
     */
    public static function buildSearchWhere(string $search, array $searchFields): array
    {
        if (empty($search) || empty($searchFields)) {
            return ['where' => '', 'params' => []];
        }

        $conditions = [];
        $params = [];

        foreach ($searchFields as $field) {
            $conditions[] = "$field LIKE :search";
        }

        $where = 'WHERE (' . implode(' OR ', $conditions) . ')';
        $params[':search'] = '%' . $search . '%';

        return ['where' => $where, 'params' => $params];
    }

    /**
     * Get total count for pagination
     */
    public static function getTotalCount(Database $db, string $sql, array $params): int
    {
        // Extract the main query without ORDER BY
        $countSql = preg_replace('/ORDER BY.*$/i', '', $sql);
        $countSql = "SELECT COUNT(*) as total FROM ($countSql) as count_query";

        $result = $db->fetchRow($countSql, $params);
        return (int)($result['total'] ?? 0);
    }

    /**
     * Render search and filter bar
     */
    public static function renderSearchFilterBar(array $options): string
    {
        $search = $options['search'] ?? '';
        $perPage = $options['per_page'] ?? 10;
        $placeholder = $options['placeholder'] ?? 'Search...';

        return '
        <div class="search-filter-bar">
            <div class="search-section">
                <div class="search-input-group">
                    <i class="fas fa-search"></i>
                    <input type="text" id="search-input" class="search-input"
                           placeholder="' . htmlspecialchars($placeholder) . '"
                           value="' . htmlspecialchars($search) . '"
                           onkeyup="handleSearch(event)">
                </div>
                <button id="search-button" class="btn btn-primary" type="button">
                    <i class="fas fa-search"></i> Search
                </button>
                <button class="btn btn-secondary" onclick="clearSearch()" type="button">
                    <i class="fas fa-times"></i> Clear
                </button>
            </div>
            <div class="filter-section">
                <div class="per-page-selector">
                    <label for="per-page-select">Show:</label>
                    <select id="per-page-select" class="per-page-select" onchange="changePerPage(this.value)">
                        <option value="10"' . ($perPage == 10 ? ' selected' : '') . '>10 per page</option>
                        <option value="20"' . ($perPage == 20 ? ' selected' : '') . '>20 per page</option>
                        <option value="50"' . ($perPage == 50 ? ' selected' : '') . '>50 per page</option>
                        <option value="100"' . ($perPage == 100 ? ' selected' : '') . '>100 per page</option>
                    </select>
                </div>
            </div>
        </div>';
    }

    /**
     * Render empty state
     */
    public static function renderEmptyState(string $icon, string $message, string $buttonText = '', string $buttonAction = ''): string
    {
        $button = '';
        if ($buttonText && $buttonAction) {
            $button = '<button class="btn btn-primary" onclick="' . htmlspecialchars($buttonAction) . '">' .
                htmlspecialchars($buttonText) . '</button>';
        }

        return '
        <div class="empty-state">
            <i class="' . htmlspecialchars($icon) . '"></i>
            <h3>' . htmlspecialchars($message) . '</h3>
            ' . $button . '
        </div>';
    }

    /**
     * Render status badge
     */
    public static function renderStatusBadge(string $status): string
    {
        $classes = [
            'active' => 'badge-success',
            'inactive' => 'badge-danger',
            'pending' => 'badge-warning',
            'confirmed' => 'badge-success',
            'cancelled' => 'badge-danger',
            'completed' => 'badge-success'
        ];

        $class = $classes[$status] ?? 'badge-secondary';
        $label = ucfirst($status);

        return '<span class="badge ' . $class . '">' . htmlspecialchars($label) . '</span>';
    }

    /**
     * Render action buttons with dependency check
     */
    public static function renderActionButtonsWithCheck(Database $db, string $table, string $id): string
    {
        return '
        <div class="card-actions">
            <button class="btn btn-sm btn-primary" onclick="editItem(\'' . $table . '\', \'' . $id . '\')" title="Edit">
                <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-sm btn-danger" onclick="deleteItem(\'' . $table . '\', \'' . $id . '\')" title="Delete">
                <i class="fas fa-trash"></i>
            </button>
        </div>';
    }

    /**
     * Render card field
     */
    public static function renderCardField(string $label, $value, string $type = 'text'): string
    {
        // Handle null values and convert to string
        if ($value === null || (empty($value) && $value !== '0')) {
            $value = '-';
        } else {
            $value = (string) $value;
        }

        return '
        <div class="card-field">
            <label>' . htmlspecialchars($label) . ':</label>
            <span>' . htmlspecialchars($value) . '</span>
        </div>';
    }

    /**
     * Format date for display
     */
    public static function formatDate(string $date, string $format = 'M j, Y'): string
    {
        if (empty($date)) {
            return '-';
        }

        try {
            return date($format, strtotime($date));
        } catch (Exception $e) {
            return $date;
        }
    }

    /**
     * Check delete dependencies
     */
    public static function checkDeleteDependencies(Database $db, string $table, string $id): array
    {
        $dependencies = [];

        switch ($table) {
            case 'categories':
                $count = $db->fetchRow("SELECT COUNT(*) as count FROM services WHERE category_id = :id", [':id' => $id])['count'];
                if ($count > 0) {
                    $dependencies[] = "$count service(s)";
                }
                break;

            case 'services':
                $count = $db->fetchRow("SELECT COUNT(*) as count FROM reservations WHERE service_id = :id", [':id' => $id])['count'];
                if ($count > 0) {
                    $dependencies[] = "$count reservation(s)";
                }
                break;

            case 'employees':
                $count = $db->fetchRow("SELECT COUNT(*) as count FROM reservations WHERE employee_id = :id", [':id' => $id])['count'];
                if ($count > 0) {
                    $dependencies[] = "$count reservation(s)";
                }
                break;

            case 'customers':
                $count = $db->fetchRow("SELECT COUNT(*) as count FROM reservations WHERE customer_id = :id", [':id' => $id])['count'];
                if ($count > 0) {
                    $dependencies[] = "$count reservation(s)";
                }
                break;
        }

        if (!empty($dependencies)) {
            return [
                'can_delete' => false,
                'message' => 'Cannot delete: This item is referenced by ' . implode(', ', $dependencies)
            ];
        }

        return ['can_delete' => true, 'message' => ''];
    }

    /**
     * Format contact information
     */
    public static function formatContactInfo(array $contact): string
    {
        $output = '';

        if (!empty($contact['email'])) {
            $output .= '<div class="contact-field">';
            $output .= '<i class="fas fa-envelope contact-icon"></i>';
            $output .= '<a href="mailto:' . htmlspecialchars($contact['email']) . '" class="contact-value">' . htmlspecialchars($contact['email']) . '</a>';
            $output .= '</div>';
        }

        if (!empty($contact['phone'])) {
            $output .= '<div class="contact-field">';
            $output .= '<i class="fas fa-phone contact-icon"></i>';
            $output .= '<a href="tel:' . htmlspecialchars($contact['phone']) . '" class="contact-value">' . htmlspecialchars($contact['phone']) . '</a>';
            $output .= '</div>';
        }

        return $output;
    }

    /**
     * Format currency value
     */
    public static function formatCurrency($amount, string $currency = '€'): string
    {
        if (is_null($amount) || $amount === '') {
            return '-';
        }

        return number_format((float)$amount, 2) . ' ' . $currency;
    }
}

/**
 * Format datetime for display
 */
function formatDisplayDateTime($datetime, $format = 'M j, Y g:i A')
{
    if (empty($datetime)) {
        return '-';
    }

    if (is_string($datetime)) {
        $datetime = new DateTime($datetime);
    }

    return $datetime->format($format);
}

/**
 * Get status badge HTML
 */
function getStatusBadge($status, $type = 'default')
{
    $classes = [
        'active' => 'badge-success',
        'inactive' => 'badge-danger',
        'pending' => 'badge-warning',
        'completed' => 'badge-success',
        'cancelled' => 'badge-danger',
        'confirmed' => 'badge-success',
        'draft' => 'badge-secondary'
    ];

    $class = $classes[$status] ?? 'badge-' . $type;
    $label = ucfirst($status);

    return "<span class=\"badge {$class}\">{$label}</span>";
}

/**
 * Generate action buttons for table rows
 */
function getActionButtons($id, $type, $actions = ['edit', 'delete'])
{
    $buttons = [];

    if (in_array('view', $actions)) {
        $buttons[] = "<button class=\"btn-icon btn-outline\" onclick=\"viewItem({$id})\" title=\"View\">
                        <i class=\"fas fa-eye\"></i>
                      </button>";
    }

    if (in_array('edit', $actions)) {
        $buttons[] = "<button class=\"btn-icon btn-primary\" onclick=\"editItem({$id})\" title=\"Edit\">
                        <i class=\"fas fa-edit\"></i>
                      </button>";
    }

    if (in_array('delete', $actions)) {
        $buttons[] = "<button class=\"btn-icon btn-danger\" onclick=\"deleteItem({$id}, '{$type}')\" title=\"Delete\">
                        <i class=\"fas fa-trash\"></i>
                      </button>";
    }

    return '<div class="action-buttons">' . implode(' ', $buttons) . '</div>';
}

/**
 * Generate table header with sorting
 */
function getTableHeader($columns, $currentSort = '', $currentOrder = 'asc')
{
    $html = '<thead><tr>';

    foreach ($columns as $key => $label) {
        $sortable = is_array($label) ? $label['sortable'] ?? true : true;
        $displayLabel = is_array($label) ? $label['label'] : $label;

        if ($sortable) {
            $orderIcon = '';
            $nextOrder = 'asc';

            if ($currentSort === $key) {
                $orderIcon = $currentOrder === 'asc' ?
                    '<i class="fas fa-sort-up"></i>' :
                    '<i class="fas fa-sort-down"></i>';
                $nextOrder = $currentOrder === 'asc' ? 'desc' : 'asc';
            } else {
                $orderIcon = '<i class="fas fa-sort"></i>';
            }

            $html .= "<th class=\"sortable\" onclick=\"sortTable('{$key}', '{$nextOrder}')\">";
            $html .= "{$displayLabel} {$orderIcon}";
            $html .= "</th>";
        } else {
            $html .= "<th>{$displayLabel}</th>";
        }
    }

    $html .= '</tr></thead>';
    return $html;
}

/**
 * Generate form field HTML
 */
function generateFormField($field, $value = '', $errors = [])
{
    $type = $field['type'] ?? 'text';
    $name = $field['name'];
    $label = $field['label'] ?? ucfirst($name);
    $required = $field['required'] ?? false;
    $placeholder = $field['placeholder'] ?? '';
    $options = $field['options'] ?? [];
    $attributes = $field['attributes'] ?? [];

    $error = $errors[$name] ?? '';
    $errorClass = $error ? 'error' : '';
    $requiredAttr = $required ? 'required' : '';

    $html = "<div class=\"form-group {$errorClass}\">";
    $html .= "<label for=\"{$name}\">{$label}" . ($required ? ' *' : '') . "</label>";

    switch ($type) {
        case 'text':
        case 'email':
        case 'password':
        case 'number':
        case 'tel':
        case 'url':
            $html .= "<input type=\"{$type}\" id=\"{$name}\" name=\"{$name}\" value=\"" . htmlspecialchars($value) . "\" placeholder=\"{$placeholder}\" {$requiredAttr}";
            foreach ($attributes as $attr => $attrValue) {
                $html .= " {$attr}=\"{$attrValue}\"";
            }
            $html .= ">";
            break;

        case 'textarea':
            $rows = $field['rows'] ?? 4;
            $html .= "<textarea id=\"{$name}\" name=\"{$name}\" rows=\"{$rows}\" placeholder=\"{$placeholder}\" {$requiredAttr}";
            foreach ($attributes as $attr => $attrValue) {
                $html .= " {$attr}=\"{$attrValue}\"";
            }
            $html .= ">" . htmlspecialchars($value) . "</textarea>";
            break;

        case 'select':
            $html .= "<select id=\"{$name}\" name=\"{$name}\" {$requiredAttr}";
            foreach ($attributes as $attr => $attrValue) {
                $html .= " {$attr}=\"{$attrValue}\"";
            }
            $html .= ">";

            if ($placeholder) {
                $html .= "<option value=\"\">{$placeholder}</option>";
            }

            foreach ($options as $optValue => $optLabel) {
                $selected = $value == $optValue ? 'selected' : '';
                $html .= "<option value=\"{$optValue}\" {$selected}>{$optLabel}</option>";
            }
            $html .= "</select>";
            break;

        case 'checkbox':
            $checked = $value ? 'checked' : '';
            $html .= "<input type=\"checkbox\" id=\"{$name}\" name=\"{$name}\" value=\"1\" {$checked} {$requiredAttr}";
            foreach ($attributes as $attr => $attrValue) {
                $html .= " {$attr}=\"{$attrValue}\"";
            }
            $html .= ">";
            break;

        case 'radio':
            foreach ($options as $optValue => $optLabel) {
                $checked = $value == $optValue ? 'checked' : '';
                $html .= "<label class=\"radio-label\">";
                $html .= "<input type=\"radio\" name=\"{$name}\" value=\"{$optValue}\" {$checked} {$requiredAttr}>";
                $html .= " {$optLabel}</label>";
            }
            break;

        case 'file':
            $accept = $field['accept'] ?? '';
            $html .= "<input type=\"file\" id=\"{$name}\" name=\"{$name}\" accept=\"{$accept}\" {$requiredAttr}";
            foreach ($attributes as $attr => $attrValue) {
                $html .= " {$attr}=\"{$attrValue}\"";
            }
            $html .= ">";
            break;

        case 'hidden':
            $html = "<input type=\"hidden\" name=\"{$name}\" value=\"" . htmlspecialchars($value) . "\">";
            break;
    }

    if ($error) {
        $html .= "<div class=\"error-message\">{$error}</div>";
    }

    if ($type !== 'hidden') {
        $html .= "</div>";
    }

    return $html;
}

/**
 * Truncate text with ellipsis
 */
function truncateText($text, $length = 50, $suffix = '...')
{
    if (strlen($text) <= $length) {
        return $text;
    }

    return substr($text, 0, $length) . $suffix;
}

/**
 * Get file size in human readable format
 */
function formatFileSize($bytes)
{
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];

    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }

    return round($bytes, 2) . ' ' . $units[$i];
}

/**
 * Generate breadcrumb navigation
 */
function generateBreadcrumb($items)
{
    $html = '<nav class="breadcrumb">';
    $html .= '<ol class="breadcrumb-list">';

    foreach ($items as $item) {
        if (isset($item['url'])) {
            $html .= "<li class=\"breadcrumb-item\"><a href=\"{$item['url']}\">{$item['label']}</a></li>";
        } else {
            $html .= "<li class=\"breadcrumb-item active\">{$item['label']}</li>";
        }
    }

    $html .= '</ol>';
    $html .= '</nav>';

    return $html;
}

/**
 * Validate form data
 */
function validateFormData($data, $rules)
{
    $errors = [];

    foreach ($rules as $field => $fieldRules) {
        $value = $data[$field] ?? '';

        foreach ($fieldRules as $rule) {
            switch ($rule) {
                case 'required':
                    if (empty($value)) {
                        $errors[$field] = ucfirst($field) . ' is required';
                    }
                    break;

                case 'email':
                    if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $errors[$field] = 'Invalid email format';
                    }
                    break;

                case 'numeric':
                    if (!empty($value) && !is_numeric($value)) {
                        $errors[$field] = ucfirst($field) . ' must be a number';
                    }
                    break;
            }
        }
    }

    return $errors;
}
