<?php
/**
 * Simple JavaScript Translations
 * Just outputs translations as JSON. No complexity.
 */

header("Content-Type: application/javascript; charset=utf-8");
header("Cache-Control: no-cache, no-store, must-revalidate");
header("Pragma: no-cache");
header("Expires: 0");

require_once __DIR__ . "/../../shared/config.php";
require_once __DIR__ . "/../../shared/database.php";
require_once __DIR__ . "/../../shared/tenant_manager.php";

Config::init();
TenantManager::init();

$language = $_GET["lang"] ?? "el";
$db = TenantManager::getDatabase();

$column = ($language === "en") ? "text_english" : "text_greek";
$translations = $db->fetchAll("SELECT key, $column as text FROM simple_translations");

$jsTranslations = [];
foreach ($translations as $trans) {
    $jsTranslations[$trans["key"]] = $trans["text"];
}

echo "// Simple Translations - " . date("Y-m-d H:i:s") . "\n";
echo "window.translations = " . json_encode($jsTranslations, JSON_UNESCAPED_UNICODE) . ";\n";
echo "window.currentLanguage = " . json_encode($language) . ";\n";
echo "\n";
echo "// Simple translation function\n";
echo "function t(key, fallback) {\n";
echo "    return window.translations[key] || fallback || key;\n";
echo "}\n";
?>