<?php

/**
 * Categories Controller
 * Handles category CRUD operations
 */

/**
 * Handle category form submission
 */
function handleCategoriesForm(array $data, Database $db): array
{
    $action = $data['action'] ?? '';
    $id = $data['id'] ?? '';

    if ($action === 'save') {
        // Validate CSRF token
        if (!Application::verifyCsrfToken($data['csrf_token'] ?? '')) {
            return ['success' => false, 'error' => 'Invalid request token'];
        }

        // Validate required fields
        if (empty(trim($data['name'] ?? ''))) {
            return ['success' => false, 'error' => 'Category name is required'];
        }

        $categoryData = [
            ':name' => Application::sanitize($data['name']),
            ':description' => Application::sanitize($data['description']),
            ':icon' => Application::sanitize($data['icon']),
            ':color' => Application::sanitize($data['color']),
            ':sort_order' => (int)($data['sort_order'] ?? 0),
            ':is_active' => isset($data['is_active']) ? 1 : 0,
            ':updated_at' => date('Y-m-d H:i:s')
        ];

        try {
            if ($id) {
                // Update existing category
                $categoryData[':id'] = $id;
                $sql = "UPDATE categories SET 
                        name = :name, 
                        description = :description, 
                        icon = :icon, 
                        color = :color, 
                        sort_order = :sort_order, 
                        is_active = :is_active, 
                        updated_at = :updated_at 
                        WHERE id = :id";
            } else {
                // Create new category
                $categoryData[':id'] = Application::generateId('CAT');
                $categoryData[':created_at'] = date('Y-m-d H:i:s');
                $sql = "INSERT INTO categories (id, name, description, icon, color, sort_order, is_active, created_at, updated_at)
                        VALUES (:id, :name, :description, :icon, :color, :sort_order, :is_active, :created_at, :updated_at)";
            }

            $result = $db->query($sql, $categoryData);

            if ($result !== false) {
                // Handle service assignments for existing categories
                if ($id) {
                    try {
                        // First, unassign all services from this category
                        $db->query("UPDATE services SET category_id = NULL WHERE category_id = :category_id", [':category_id' => $id]);

                        // Then assign selected services to this category
                        $serviceIds = $data['service_ids'] ?? [];
                        if (!empty($serviceIds)) {
                            foreach ($serviceIds as $serviceId) {
                                $db->query(
                                    "UPDATE services SET category_id = :category_id WHERE id = :service_id",
                                    [':category_id' => $id, ':service_id' => $serviceId]
                                );
                            }
                        }
                    } catch (Exception $e) {
                        error_log("Failed to update service assignments: " . $e->getMessage());
                        // Continue anyway - category was saved successfully
                    }
                }

                // Auto-create translation entries for new category
                if (!$id) { // Only for new categories (when $id is empty)
                    try {
                        require_once __DIR__ . '/../../shared/translation.php';
                        // Always create name translation
                        Translation::save("category_name_{$categoryData[':id']}", $categoryData[':name'], $categoryData[':name'], 'categories');
                        // Always create description translation, even if empty
                        $description = $categoryData[':description'] ?? '';
                        Translation::save("category_description_{$categoryData[':id']}", $description, $description, 'categories');
                    } catch (Exception $e) {
                        // Text creation failed, but category was created successfully
                        error_log("Failed to create category translations: " . $e->getMessage());
                    }
                }

                return [
                    'success' => true,
                    'redirect' => '/store-admin/?page=categories',
                    'message' => $id ? 'Category updated successfully' : 'Category created successfully'
                ];
            } else {
                return ['success' => false, 'error' => 'Failed to save category'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
        }
    }

    return ['success' => false, 'error' => 'Invalid action'];
}
