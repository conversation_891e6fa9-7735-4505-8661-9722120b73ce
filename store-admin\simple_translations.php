<?php
/**
 * Simple Translation Admin
 * Edit translations. Save. Done.
 */

require_once __DIR__ . "/../shared/config.php";
require_once __DIR__ . "/../shared/database.php";
require_once __DIR__ . "/../shared/tenant_manager.php";

Config::init();
TenantManager::init();
$db = TenantManager::getDatabase();

// Handle save
if ($_POST && isset($_POST["save"])) {
    $saved = 0;
    foreach ($_POST as $key => $value) {
        if (strpos($key, "greek_") === 0) {
            $translationKey = substr($key, 6);
            $greekText = $value;
            $englishText = $_POST["english_" . $translationKey] ?? "";
            
            $result = $db->query("
                UPDATE simple_translations 
                SET text_greek = ?, text_english = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE key = ?
            ", [$greekText, $englishText, $translationKey]);
            
            if ($result) $saved++;
        }
    }
    $message = "Saved $saved translations successfully!";
}

// Get all translations
$search = $_GET["search"] ?? "";
$whereClause = "";
$params = [];

if ($search) {
    $whereClause = "WHERE key LIKE ? OR text_greek LIKE ? OR text_english LIKE ?";
    $params = ["%$search%", "%$search%", "%$search%"];
}

$translations = $db->fetchAll("
    SELECT * FROM simple_translations 
    $whereClause 
    ORDER BY key
", $params);

?>
<!DOCTYPE html>
<html>
<head>
    <title>Simple Translations</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; margin-bottom: 20px; }
        .search { margin-bottom: 20px; }
        .search input { padding: 10px; width: 300px; }
        .search button { padding: 10px 20px; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 10px; text-align: left; }
        th { background: #f5f5f5; }
        textarea { width: 100%; min-height: 40px; resize: vertical; }
        .save-btn { background: #007cba; color: white; padding: 15px 30px; border: none; font-size: 16px; margin: 20px 0; }
        .message { background: #d4edda; color: #155724; padding: 15px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Simple Translations</h1>
        <p>Edit client interface text in Greek and English. Changes appear immediately on client.</p>
    </div>
    
    <?php if (isset($message)): ?>
        <div class="message"><?= $message ?></div>
    <?php endif; ?>
    
    <div class="search">
        <form method="get">
            <input type="text" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="Search translations...">
            <button type="submit">Search</button>
            <a href="?">Show All</a>
        </form>
    </div>
    
    <form method="post">
        <table>
            <thead>
                <tr>
                    <th>Key</th>
                    <th>Greek Text</th>
                    <th>English Text</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($translations as $trans): ?>
                <tr>
                    <td><strong><?= htmlspecialchars($trans["key"]) ?></strong></td>
                    <td>
                        <textarea name="greek_<?= htmlspecialchars($trans["key"]) ?>"><?= htmlspecialchars($trans["text_greek"]) ?></textarea>
                    </td>
                    <td>
                        <textarea name="english_<?= htmlspecialchars($trans["key"]) ?>"><?= htmlspecialchars($trans["text_english"]) ?></textarea>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <button type="submit" name="save" class="save-btn">Save All Translations</button>
    </form>
    
    <p><strong>Total translations:</strong> <?= count($translations) ?></p>
</body>
</html>