<?php

/**
 * System Core Configuration
 * Configuration and authentication for system administration with security controls
 */

// Security: Only allow from localhost or specific IPs
function checkSystemAccess(): bool
{
    $allowedIPs = [
        '127.0.0.1',
        '::1',
        'localhost',
        '*************', /*GK Prepei na afairethi */
        '*************' /*GK Prepei na afairethi */
    ];

    $clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
    $serverName = $_SERVER['SERVER_NAME'] ?? '';
    $httpHost = $_SERVER['HTTP_HOST'] ?? '';

    // Allow localhost
    if (in_array($clientIP, $allowedIPs) || $serverName === 'localhost') {
        return true;
    }

    // Check for development environment
    if (strpos($serverName, '.local') !== false || strpos($httpHost, '.local') !== false) {
        return true;
    }

    // Allow access from production domains (any domain with proper structure)
    // Check if this is a valid domain structure (not localhost/IP)
    if (!empty($httpHost) && !filter_var($httpHost, FILTER_VALIDATE_IP)) {
        $hostParts = explode('.', $httpHost);
        // Allow if it's a proper domain (at least 2 parts) and not a local development domain
        if (count($hostParts) >= 2 && !str_contains($httpHost, '.local') && !str_contains($httpHost, 'localhost')) {
            return true;
        }
    }

    // Allow if running from command line (no HTTP_HOST set)
    if (php_sapi_name() === 'cli') {
        return true;
    }

    // For development/testing - allow if accessing system-admin directly
    if (strpos($_SERVER['REQUEST_URI'] ?? '', '/system-admin/') !== false) {
        return true;
    }

    return false;
}

// System authentication
function requireSystemAuth(): void
{
    if (!checkSystemAccess()) {
        http_response_code(403);
        die('Access denied. System management only available from localhost.');
    }

    // Configure session before starting it
    ini_set('session.gc_maxlifetime', 86400);
    ini_set('session.cookie_lifetime', 86400);
    ini_set('session.use_strict_mode', 1);
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_samesite', 'Lax');

    if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
        ini_set('session.cookie_secure', 1);
    }

    session_start();

    // System admin authentication with secure password
    if (!isset($_SESSION['system_admin_logged_in'])) {
        if (isset($_POST['system_password'])) {
            $systemPassword = Environment::get('SYSTEM_ADMIN_PASSWORD');
            
            if (empty($systemPassword)) {
                $error = 'System password not configured. Please set SYSTEM_ADMIN_PASSWORD in environment variables.';
            } elseif (password_verify($_POST['system_password'], $systemPassword) || 
                     (strlen($systemPassword) < 60 && $_POST['system_password'] === $systemPassword)) {
                $_SESSION['system_admin_logged_in'] = true;
                $_SESSION['system_admin_login_time'] = time();
                // Use JavaScript redirect to avoid header issues with POST data
                echo '<script>window.location.href = "' . $_SERVER['PHP_SELF'] . '";</script>';
                exit;
            } else {
                $error = 'Invalid password';
                // Log failed attempt
                error_log('Failed system admin login attempt from IP: ' . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
            }
        }

        showLoginForm($error ?? '');
        exit;
    }
}

function showLoginForm(string $error = ''): void
{
?>
    <!DOCTYPE html>
    <html>

    <head>
        <title>System Access</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                background: #f5f5f5;
                margin: 0;
                padding: 50px;
            }

            .login-form {
                background: white;
                padding: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                max-width: 300px;
                margin: 0 auto;
            }

            .form-group {
                margin-bottom: 15px;
            }

            label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
            }

            input[type="password"] {
                width: 100%;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                box-sizing: border-box;
            }

            button {
                width: 100%;
                padding: 12px;
                background: #007cba;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }

            button:hover {
                background: #005a87;
            }

            .error {
                color: red;
                margin-bottom: 15px;
            }

            .warning {
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                padding: 10px;
                border-radius: 4px;
                margin-bottom: 15px;
                color: #856404;
            }
        </style>
    </head>

    <body>
        <div class="login-form">
            <h2>System Administration</h2>
            <div class="warning">
                <strong>⚠️ Warning:</strong> This is a system administration interface. Only authorized personnel should access this area.
            </div>

            <?php if ($error): ?>
                <div class="error"><?= htmlspecialchars($error) ?></div>
            <?php endif; ?>

            <form method="post">
                <div class="form-group">
                    <label for="system_password">System Password:</label>
                    <input type="password" id="system_password" name="system_password" required>
                </div>
                <button type="submit">Access System</button>
            </form>
        </div>
    </body>

    </html>
<?php
}

// System constants
const SYSTEM_VERSION = '1.0.0';
const SYSTEM_NAME = 'GK Radevou System Core';

// Include shared files
require_once __DIR__ . '/../shared/environment.php';
require_once __DIR__ . '/../shared/config.php';
require_once __DIR__ . '/../shared/database.php';
require_once __DIR__ . '/../shared/tenant_manager.php';
require_once __DIR__ . '/../shared/functions.php';

// Load environment variables
Environment::load();

// Initialize configuration
Config::init();

// System utilities
function formatFileSize(int $bytes): string
{
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);

    $bytes /= pow(1024, $pow);

    return round($bytes, 2) . ' ' . $units[$pow];
}

function getDatabaseSize(string $path): int
{
    return file_exists($path) ? filesize($path) : 0;
}

function getSystemStats(): array
{
    $masterDb = Database::master();
    $tenants = $masterDb->fetchAll("SELECT * FROM tenants ORDER BY created_at DESC");

    $stats = [
        'tenants_total' => count($tenants),
        'tenants_active' => 0,
        'tenants_inactive' => 0,
        'total_db_size' => 0,
        'total_customers' => 0,
        'total_bookings' => 0,
        'system_uptime' => getSystemUptime()
    ];

    foreach ($tenants as $tenant) {
        if ($tenant['status'] === 'active') {
            $stats['tenants_active']++;
        } else {
            $stats['tenants_inactive']++;
        }

        // Get tenant database size
        $dbPath = Config::getTenantDbPath($tenant['subdomain']);
        $stats['total_db_size'] += getDatabaseSize($dbPath);

        // Get tenant statistics
        try {
            $tenantDb = Database::tenant($tenant['subdomain']);
            $customers = $tenantDb->fetchRow("SELECT COUNT(*) as count FROM customers");
            $bookings = $tenantDb->fetchRow("SELECT COUNT(*) as count FROM reservations");

            $stats['total_customers'] += $customers['count'] ?? 0;
            $stats['total_bookings'] += $bookings['count'] ?? 0;
        } catch (Exception $e) {
            // Skip if database is corrupted
        }
    }

    // Add master database size
    $stats['total_db_size'] += getDatabaseSize(Config::MASTER_DB);

    return $stats;
}

function getSystemUptime(): string
{
    if (function_exists('sys_getloadavg')) {
        $load = sys_getloadavg();
        return sprintf("Load: %.2f, %.2f, %.2f", $load[0], $load[1], $load[2]);
    }

    return 'N/A';
}

function cleanupLogs(): int
{
    $logDir = __DIR__ . '/../storage/logs/';
    $cleaned = 0;

    if (is_dir($logDir)) {
        $files = glob($logDir . '*.log');
        $oneWeekAgo = time() - (7 * 24 * 60 * 60);

        foreach ($files as $file) {
            if (filemtime($file) < $oneWeekAgo) {
                if (unlink($file)) {
                    $cleaned++;
                }
            }
        }
    }

    return $cleaned;
}

function backupDatabase(string $tenantId): bool
{
    $sourcePath = Config::getTenantDbPath($tenantId);
    $backupDir = __DIR__ . '/../storage/backups/';

    if (!is_dir($backupDir)) {
        mkdir($backupDir, 0755, true);
    }

    $backupPath = $backupDir . $tenantId . '_' . date('Y-m-d_H-i-s') . '.sqlite';

    return copy($sourcePath, $backupPath);
}

function getTenantLogs(string $tenantId, int $limit = 100): array
{
    $logFile = __DIR__ . '/../storage/logs/' . $tenantId . '.log';
    $logs = [];

    if (file_exists($logFile)) {
        $lines = file($logFile, FILE_IGNORE_NEW_LINES);
        $logs = array_slice(array_reverse($lines), 0, $limit);
    }

    return $logs;
}
