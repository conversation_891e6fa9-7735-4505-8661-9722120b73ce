<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Store Admin - Style Test</title>
    <link rel="stylesheet" href="/store-admin/assets/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="main" style="padding: 20px; background: #f9fafb; min-height: 100vh;">
        <h1>Store Admin Styling Test</h1>
        
        <!-- Test Search Bar -->
        <div class="card" style="margin-bottom: 20px;">
            <div class="card-header">
                <h3>Search Bar Test</h3>
            </div>
            <div class="card-body">
                <div class="search-bar">
                    <input type="text" placeholder="Search customers..." id="test-search">
                    <i class="fas fa-search search-icon"></i>
                </div>
                <p style="margin-top: 10px; font-size: 14px; color: #666;">
                    ✅ Search icon should appear inside the left side of the input field
                </p>
            </div>
        </div>

        <!-- Test Status Badges -->
        <div class="card" style="margin-bottom: 20px;">
            <div class="card-header">
                <h3>Status Badges Test</h3>
            </div>
            <div class="card-body">
                <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                    <span class="status-badge confirmed">Confirmed</span>
                    <span class="status-badge pending">Pending</span>
                    <span class="status-badge cancelled">Cancelled</span>
                    <span class="status-badge completed">Completed</span>
                </div>
            </div>
        </div>

        <!-- Test Buttons -->
        <div class="card" style="margin-bottom: 20px;">
            <div class="card-header">
                <h3>Button Test</h3>
            </div>
            <div class="card-body">
                <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                    <button class="btn btn-primary">
                        <i class="fas fa-plus"></i> Primary
                    </button>
                    <button class="btn btn-secondary">Secondary</button>
                    <button class="btn btn-success">Success</button>
                    <button class="btn btn-warning">Warning</button>
                    <button class="btn btn-danger">Danger</button>
                </div>
            </div>
        </div>

        <!-- Test Dashboard Components -->
        <div class="card" style="margin-bottom: 20px;">
            <div class="card-header">
                <h3>Dashboard Components Test</h3>
            </div>
            <div class="card-body">
                <!-- Stats Grid -->
                <div class="stats-grid" style="margin-bottom: 20px;">
                    <div class="stat-item">
                        <div class="stat-value">127</div>
                        <div class="stat-label">Customers</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">89</div>
                        <div class="stat-label">Services</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">34</div>
                        <div class="stat-label">Today</div>
                    </div>
                </div>

                <!-- Reservation List -->
                <h4>Reservation List:</h4>
                <div class="reservation-list">
                    <div class="reservation-item">
                        <div class="reservation-time">09:00</div>
                        <div class="reservation-details">
                            <div class="reservation-customer">John Doe</div>
                            <div class="reservation-service">Haircut - Maria</div>
                        </div>
                        <div class="reservation-status">
                            <span class="status-badge confirmed">Confirmed</span>
                        </div>
                    </div>
                    <div class="reservation-item">
                        <div class="reservation-time">10:30</div>
                        <div class="reservation-details">
                            <div class="reservation-customer">Jane Smith</div>
                            <div class="reservation-service">Color Treatment - Sarah</div>
                        </div>
                        <div class="reservation-status">
                            <span class="status-badge pending">Pending</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Action Buttons -->
        <div class="card">
            <div class="card-header">
                <h3>Action Buttons Test</h3>
            </div>
            <div class="card-body">
                <div class="action-buttons">
                    <button class="btn btn-success">
                        <i class="fas fa-plus"></i> New Reservation
                    </button>
                    <button class="btn btn-info">
                        <i class="fas fa-user-plus"></i> Add Customer
                    </button>
                    <button class="btn btn-warning">
                        <i class="fas fa-cut"></i> Add Service
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-users"></i> Add Employee
                    </button>
                </div>
            </div>
        </div>

        <div style="margin-top: 40px; padding: 20px; background: white; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="color: #047857; margin: 0 0 10px 0;">✅ Styling Fixed!</h3>
            <ul style="margin: 0; color: #059669;">
                <li>✅ Search icon properly positioned with z-index</li>
                <li>✅ Single CSS file (no more conflicts)</li>
                <li>✅ All dashboard components styled</li>
                <li>✅ Responsive design working</li>
                <li>✅ Status badges with proper colors</li>
                <li>✅ Action buttons with hover effects</li>
            </ul>
        </div>
    </div>
</body>
</html>
