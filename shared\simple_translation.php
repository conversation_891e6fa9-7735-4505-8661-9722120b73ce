<?php
/**
 * Simple Translation Function
 * Just gets text from database. No complexity.
 */

function getSimpleTranslation($key, $language = "el") {
    static $cache = [];
    static $db = null;
    
    if ($db === null) {
        require_once __DIR__ . "/database.php";
        require_once __DIR__ . "/tenant_manager.php";
        $db = TenantManager::getDatabase();
    }
    
    $cacheKey = $key . "_" . $language;
    
    if (!isset($cache[$cacheKey])) {
        $column = ($language === "en") ? "text_english" : "text_greek";
        $result = $db->fetchRow("SELECT $column FROM simple_translations WHERE key = ?", [$key]);
        $cache[$cacheKey] = $result ? $result[$column] : $key;
    }
    
    return $cache[$cacheKey];
}

// Alias for shorter usage
function st($key, $language = "el") {
    return getSimpleTranslation($key, $language);
}
?>